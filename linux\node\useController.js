import { ref } from 'vue';
import { triggerVibrate } from '@/utils/common';
import { getNodeList as getNodeListApi } from '@/api/node';
import throttle from '@/uni_modules/uv-ui-tools/libs/function/throttle.js';
import { saveCurrentPanelConfig, setupNodeConfig, setNodeAccessFlag } from '@/utils/nodeConfigManager';

/**
 * 节点管理加载状态
 */
export const loading = ref(false);

/**
 * 当前选中的节点
 */
export const currentNode = ref(null);
export const currentNodeIndex = ref(-1);

/**
 * 节点列表
 */
export const nodeList = ref([]);
export const nodePaging = ref(null);
export const pageContainer = ref(null);

/**
 * 上下文菜单状态
 */
export const showContextMenu = ref(false);
export const showDeleteDialog = ref(false);
export const menuPosition = ref({ top: '0px', left: '0px', class: '' });
export const clonePosition = ref({ top: '0px', left: '0px' });

// 长按相关常量
export const LONG_PRESS_THRESHOLD = 500;
export const MOVE_THRESHOLD = 10;

// 触摸状态变量
let touchStartTime = 0;
let touchStartPos = { x: 0, y: 0 };
let isTouchMoved = false;
let longPressTimer = null;

/**
 * 格式化节点数据
 * @param {Object} rawNode - 原始节点数据
 * @returns {Object} 格式化后的节点数据
 */
const formatNodeData = (rawNode) => {
	// 判断节点状态：data.status === 0 表示在线
	const isOnline = rawNode.data && rawNode.data.status === 0;

	// 格式化CPU信息
	const cpuInfo = rawNode.data ? `${rawNode.data.cpu_usage || 0}核 (${rawNode.data.cpu || 0}%)` : '未知';

	// 格式化内存信息
	const memoryInfo = rawNode.data ? `${rawNode.data.memNewTotal || '未知'} (${rawNode.data.memory || 0}%)` : '未知';

	// 判断是否为本机节点
	const isLocalNode = rawNode.api_key === 'local';

	return {
		id: rawNode.id,
		name: rawNode.remarks || `节点${rawNode.id}`,
		address: rawNode.address,
		status: isOnline ? 'online' : 'offline',
		cpu: cpuInfo,
		memory: memoryInfo,
		category: '默认分类', // 接口暂无分类字段，使用默认值
		server_ip: rawNode.server_ip,
		api_key: rawNode.api_key,
		isLocal: isLocalNode, // 标记是否为本机节点
		rawData: rawNode, // 保留原始数据以备后用
	};
};

/**
 * 加载节点数据
 */
export const loadNodes = async () => {
	loading.value = true;
	try {
		const res = await getNodeListApi({ p: 1, limit: 50 });
		if (res && res.data && Array.isArray(res.data)) {
			// 格式化节点数据
			// nodeList.value = res.data.map(formatNodeData);
			nodeList.value = [
				{
					id: 1,
					name: '节点1',
					address: '***********',
					status: 'online',
					cpu: '1核 (100%)',
					memory: '100MB (100%)',
					category: '默认分类',
				},
				{
					id: 2,
					name: '节点2',
					address: '***********',
					status: 'online',
					cpu: '1核 (100%)',
					memory: '100MB (100%)',
					category: '默认分类',
				},
				{
					id: 3,
					name: '节点3',
					address: '***********',
					status: 'online',
					cpu: '1核 (100%)',
					memory: '100MB (100%)',
					category: '默认分类',
				},
				{
					id: 4,
					name: '节点4',
					address: '***********',
					status: 'online',
					cpu: '1核 (100%)',
					memory: '100MB (100%)',
					category: '默认分类',
				},
				{
					id: 5,
					name: '节点5',
					address: '***********',
					status: 'online',
					cpu: '1核 (100%)',
					memory: '100MB (100%)',
					category: '默认分类',
				},
			];
		} else {
			nodeList.value = [];
		}
	} catch (error) {
		console.error('获取节点列表失败:', error);
		pageContainer.value?.notify?.error('获取节点列表失败');
		nodeList.value = [];
	} finally {
		loading.value = false;
	}
};

/**
 * 获取节点列表
 * @returns {Promise} 节点列表Promise
 */
export const getNodeList = async (page, pageSize) => {
	loading.value = true;
	try {
		const res = await getNodeListApi({ p: page, limit: pageSize });
		if (res && res.data && Array.isArray(res.data)) {
			// 格式化节点数据
			// const formattedNodes = res.data.map(formatNodeData);
			return formattedNodes;
		}
		return [];
	} catch (e) {
		console.error('获取节点列表失败:', e);
		return [];
	} finally {
		loading.value = false;
	}
};

/**
 * 刷新成功提示
 */
export const reload = (reloadType) => {
	if (reloadType === 'complete') {
		pageContainer.value?.notify?.success('刷新成功');
	}
};

/**
 * 编辑节点
 */
export const handleEditNode = () => {
	if (!currentNode.value) return;

	// 检查是否为本机节点
	if (currentNode.value.isLocal) {
		pageContainer.value?.notify?.warning('本机节点不可编辑');
		hideContextMenu();
		return;
	}

	// 这里可以跳转到编辑节点页面或显示编辑弹窗
	uni.showToast({
		title: `编辑节点: ${currentNode.value.name}`,
		icon: 'none',
	});

	hideContextMenu();
};

/**
 * 访问节点
 */
export const handleAccessNode = async () => {
	if (!currentNode.value) return;

	// 检查是否为本机节点
	if (currentNode.value.isLocal) {
		pageContainer.value?.notify?.warning('本机节点不可访问');
		hideContextMenu();
		return;
	}

	try {
		// 显示加载提示
		uni.showLoading({
			title: '正在连接节点...',
			mask: true,
		});

		// 1. 保存当前配置
		saveCurrentPanelConfig();

		// 2. 设置节点配置
		await setupNodeConfig(currentNode.value);

		// 3. 设置节点访问标记
		setNodeAccessFlag(true, currentNode.value.id);

		// 4. 跳转到Linux主页
		throttle(() => {
			uni.hideLoading();
			uni.navigateTo({
				url: `/linux/index/index?fromNode=true&nodeId=${currentNode.value.id}`,
				animationType: 'zoom-fade-out',
			});
		}, 500);
	} catch (error) {
		uni.hideLoading();
		console.error('节点访问失败:', error);
		pageContainer.value?.notify?.error('节点访问失败，请检查节点状态');
	}

	hideContextMenu();
};

/**
 * 确认删除节点
 */
export const confirmDeleteNode = async (close) => {
	if (!currentNode.value) return;

	// 检查是否为本机节点
	if (currentNode.value.isLocal) {
		pageContainer.value?.notify?.warning('本机节点不可删除');
		showDeleteDialog.value = false;
		setTimeout(() => {
			hideContextMenu();
		}, 300);
		return;
	}

	loading.value = true;
	try {
		// 这里可以替换为实际的删除API调用
		await new Promise((resolve) => setTimeout(resolve, 500));

		// 刷新列表
		nodePaging.value?.reload();
		pageContainer.value?.notify?.success('删除成功');

		close && close();
	} catch (error) {
		console.error('删除节点失败:', error);
		pageContainer.value?.notify?.error('删除失败');
	} finally {
		loading.value = false;
	}

	showDeleteDialog.value = false;
	setTimeout(() => {
		hideContextMenu();
	}, 300);
};

/**
 * 长按触摸事件处理 - 开始触摸
 */
export const handleTouchStart = (event) => {
	// 清除可能存在的定时器
	if (longPressTimer) {
		clearTimeout(longPressTimer);
	}

	// 记录触摸开始时间和位置
	touchStartTime = Date.now();
	touchStartPos = {
		x: event.touches[0].clientX,
		y: event.touches[0].clientY,
	};
	isTouchMoved = false;

	// 设置长按定时器
	longPressTimer = setTimeout(() => {
		if (!isTouchMoved) {
			const dataset = event.currentTarget.dataset;
			const node = JSON.parse(dataset.node);
			const index = dataset.index;
			showFloatingMenu(node, event, index);
		}
	}, LONG_PRESS_THRESHOLD);
};

/**
 * 长按触摸事件处理 - 移动触摸
 */
export const handleTouchMove = (event) => {
	if (!touchStartPos) return;

	// 计算移动距离
	const moveX = Math.abs(event.touches[0].clientX - touchStartPos.x);
	const moveY = Math.abs(event.touches[0].clientY - touchStartPos.y);

	// 如果移动超过阈值，标记为已移动并取消长按定时器
	if (moveX > MOVE_THRESHOLD || moveY > MOVE_THRESHOLD) {
		isTouchMoved = true;

		if (longPressTimer) {
			clearTimeout(longPressTimer);
			longPressTimer = null;
		}

		if (showContextMenu.value) {
			hideContextMenu();
		}
	}
};

/**
 * 长按触摸事件处理 - 结束触摸
 */
export const handleTouchEnd = (event) => {
	// 清除长按定时器
	if (longPressTimer) {
		clearTimeout(longPressTimer);
		longPressTimer = null;
	}

	// 如果未移动且是短触摸（非长按），则可以处理点击事件
	if (!isTouchMoved && Date.now() - touchStartTime < LONG_PRESS_THRESHOLD) {
		const node = JSON.parse(event.currentTarget.dataset.node);
		// 这里可以添加点击节点时的处理逻辑
		// handleNodeDetail(node);
	}
};

/**
 * 长按触摸事件处理 - 取消触摸
 */
export const handleTouchCancel = () => {
	// 清除长按定时器
	if (longPressTimer) {
		clearTimeout(longPressTimer);
		longPressTimer = null;
	}

	if (showContextMenu.value) {
		hideContextMenu();
	}
};

/**
 * 显示悬浮菜单
 */
export const showFloatingMenu = (node, event, index) => {
	// 触感反馈
	triggerVibrate();

	currentNode.value = node;
	currentNodeIndex.value = index;

	// 获取被长按元素的位置
	uni.createSelectorQuery()
		.selectAll('.node-item-container')
		.boundingClientRect((rects) => {
			if (!rects || !rects[index]) return;

			const rect = rects[index];
			const systemInfo = uni.getSystemInfoSync();
			const screenHeight = systemInfo.windowHeight;
			const screenWidth = systemInfo.windowWidth;

			// 设置克隆项位置
			clonePosition.value = {
				position: 'fixed',
				top: `${rect.top}px`,
				left: `${rect.left}px`,
				width: `${rect.width}px`,
				height: `${rect.height}px`,
				transform: 'scale(1.02)',
				zIndex: '901',
				backgroundColor: 'rgba(255, 255, 255, 0.95)',
				borderRadius: '14rpx',
				boxShadow: '0 4rpx 20rpx rgba(0, 0, 0, 0.1)',
			};

			// 菜单尺寸（rpx转px）
			const menuWidth = uni.upx2px(340);
			const menuHeight = uni.upx2px(160);
			const gap = uni.upx2px(20); // 菜单与元素之间的间距

			let menuTop, menuLeft, menuClass;

			// 计算最佳显示位置
			const spaceRight = screenWidth - rect.right;
			const spaceLeft = rect.left;
			const spaceTop = rect.top;
			const spaceBottom = screenHeight - rect.bottom;

			// 水平位置计算
			if (spaceRight >= menuWidth + gap) {
				// 右侧有足够空间
				menuLeft = rect.right + gap;
				menuClass = 'menu-right';
			} else if (spaceLeft >= menuWidth + gap) {
				// 左侧有足够空间
				menuLeft = rect.left - menuWidth - gap;
				menuClass = 'menu-left';
			} else {
				// 水平居中
				menuLeft = rect.left + rect.width / 2;
				menuClass = 'menu-center';
			}

			// 垂直位置计算
			if (spaceBottom >= menuHeight + gap) {
				// 下方有足够空间
				menuTop = rect.bottom + (gap - 2);
				menuClass += ' menu-bottom';
			} else if (spaceTop >= menuHeight + gap) {
				// 上方有足够空间
				menuTop = rect.top - menuHeight - gap;
				menuClass += ' menu-top';
			} else {
				// 垂直居中
				menuTop = rect.top + rect.height / 2 - menuHeight / 2;
				menuClass += ' menu-middle';
			}

			// 确保菜单不超出屏幕边界
			menuTop = Math.max(gap, Math.min(screenHeight - menuHeight - gap, menuTop));
			if (menuClass.includes('menu-center')) {
				// 水平居中时，不需要调整左边距，因为会使用transform来居中
				menuLeft = Math.max(menuWidth / 2 + gap, Math.min(screenWidth - menuWidth / 2 - gap, menuLeft));
			} else {
				menuLeft = Math.max(gap, Math.min(screenWidth - menuWidth - gap, menuLeft));
			}

			// 设置菜单位置
			menuPosition.value = {
				top: `${menuTop}px`,
				left: `${menuLeft}px`,
				class: menuClass,
				width: `${menuWidth}px`,
			};

			// 显示菜单
			showContextMenu.value = true;
		})
		.exec();
};

/**
 * 隐藏上下文菜单
 */
export const hideContextMenu = () => {
	showContextMenu.value = false;
	currentNode.value = null;
	currentNodeIndex.value = -1;
};
